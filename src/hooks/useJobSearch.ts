import { useState, useMemo, useEffect } from 'react';
import { Job, JobFilters, SortOption } from '@/types/job';
import { supabase } from '@/integrations/supabase/client';

export const useJobSearch = () => {
  const [filters, setFilters] = useState<JobFilters>({
    search: '',
    category: '',
    location: '',
    contractType: '',
    experienceLevel: ''
  });
  
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const [allJobs, setAllJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const jobsPerPage = 9;

  // Load jobs from Supabase
  useEffect(() => {
    loadJobs();
  }, []);

  const loadJobs = async () => {
    try {
      const { data, error } = await supabase
        .from('jobs')
        .select('*')
        // Temporarily commented out until migration is applied
        // .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading jobs:', error);
      } else {
        // Transform Supabase data to match Job interface
        const transformedJobs: Job[] = (data || []).map(job => ({
          id: job.id,
          title: job.title,
          company: job.company,
          location: job.location,
          category: job.category,
          description: job.description,
          requirements: '', // Default empty since not stored separately in DB
          contractType: 'full-time' as const,
          experienceLevel: 'entry' as const,
          postedDate: new Date(job.created_at),
          source: job.external_id?.startsWith('manual-') ? 'manual' : 'hardfranca',
          isActive: job.is_active,
          deactivatedAt: job.deactivated_at ? new Date(job.deactivated_at) : null
        }));
        setAllJobs(transformedJobs);
      }
    } catch (error) {
      console.error('Exception loading jobs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredJobs = useMemo(() => {
    if (isLoading) return [];
    
    let filtered = allJobs.filter((job) => {
      const searchMatch = !filters.search || 
        job.title.toLowerCase().includes(filters.search.toLowerCase()) ||
        job.company.toLowerCase().includes(filters.search.toLowerCase()) ||
        job.description.toLowerCase().includes(filters.search.toLowerCase());

      const categoryMatch = !filters.category || job.category === filters.category;
      const locationMatch = !filters.location || job.location === filters.location;
      const contractMatch = !filters.contractType || job.contractType === filters.contractType;
      const experienceMatch = !filters.experienceLevel || job.experienceLevel === filters.experienceLevel;

      return searchMatch && categoryMatch && locationMatch && contractMatch && experienceMatch;
    });

    // Sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return b.postedDate.getTime() - a.postedDate.getTime();
        case 'oldest':
          return a.postedDate.getTime() - b.postedDate.getTime();
        case 'relevance':
        case 'salary-high':
        case 'salary-low':
        default:
          return b.postedDate.getTime() - a.postedDate.getTime();
      }
    });

    return filtered;
  }, [allJobs, filters, sortBy, isLoading]);

  const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
  const currentJobs = filteredJobs.slice(
    (currentPage - 1) * jobsPerPage,
    currentPage * jobsPerPage
  );

  const handleFiltersChange = (newFilters: JobFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleSortChange = (newSort: SortOption) => {
    setSortBy(newSort);
    setCurrentPage(1); // Reset to first page when sort changes
  };

  return {
    filters,
    sortBy,
    currentPage,
    totalPages,
    currentJobs,
    totalJobs: filteredJobs.length,
    handleFiltersChange,
    handleSortChange,
    setCurrentPage
  };
};