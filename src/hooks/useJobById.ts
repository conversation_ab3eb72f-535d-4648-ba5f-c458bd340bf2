import { useState, useEffect } from 'react';
import { Job } from '@/types/job';
import { supabase } from '@/integrations/supabase/client';

export const useJobById = (id: string) => {
  const [job, setJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('ID da vaga não fornecido');
      setIsLoading(false);
      return;
    }

    loadJob(id);
  }, [id]);

  const loadJob = async (jobId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('jobs')
        .select('*')
        .eq('id', jobId)
        .eq('is_active', true)
        .single();

      if (supabaseError) {
        console.error('Error loading job:', supabaseError);
        setError('Erro ao carregar a vaga');
        setJob(null);
      } else if (data) {
        // Transform Supabase data to match Job interface
        const transformedJob: Job = {
          id: data.id,
          title: data.title,
          company: data.company || 'Empresa não informada',
          location: data.location || 'Localização não informada',
          category: data.category || 'Geral',
          description: data.description,
          requirements: data.requirements || 'Requisitos não especificados',
          contractType: data.contract_type || 'full-time',
          experienceLevel: data.experience_level || 'entry',
          salary: data.salary,
          postedDate: new Date(data.created_at),
          applicationUrl: data.application_url || '#',
          companyLogo: data.company_logo,
          isUrgent: data.is_urgent || false,
          isFeatured: data.is_featured || false,
          source: data.external_id?.startsWith('manual-') ? 'manual' : 'hardfranca',
          isActive: data.is_active,
          deactivatedAt: data.deactivated_at ? new Date(data.deactivated_at) : null
        };
        setJob(transformedJob);
      } else {
        setError('Vaga não encontrada');
        setJob(null);
      }
    } catch (error) {
      console.error('Exception loading job:', error);
      setError('Erro inesperado ao carregar a vaga');
      setJob(null);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    job,
    isLoading,
    error,
    refetch: () => loadJob(id)
  };
};
